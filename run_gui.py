#!/usr/bin/env python3
"""
BlackJack Bot ML - Phase 4 GUI Interface Entry Point.

This script launches the comprehensive GUI interface for the BlackJack Bot ML system,
providing an intuitive way to interact with all AI agents through button-based card selection.

Features:
- Button-based card selection (13 rank buttons: A, 2-10, J, Q, K)
- Visual card display with suit symbols and hand values
- Integration with all agent types (BasicStrategy, Personas, RL)
- Real-time AI advice with confidence levels and reasoning
- Configuration management for game rules and settings
- Comprehensive help and documentation

Usage:
    python run_gui.py

Requirements:
    - Python 3.8+
    - tkinter (usually included with Python)
    - All BlackJack Bot ML dependencies

Author: Augment Agent
Phase: 4 (GUI Interface)
"""

import sys
import os
import traceback
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    # Check tkinter
    try:
        import tkinter as tk
        from tkinter import ttk
    except ImportError:
        missing_deps.append("tkinter")
    
    # Check core modules
    try:
        from core.card import Card, Suit, Rank
        from core.game_logic import BlackjackGame, GameAction
        from agents.basic_strategy_agent import BasicStrategyAgent
    except ImportError as e:
        missing_deps.append(f"core modules ({str(e)})")
    
    # Check GUI modules
    try:
        from gui.main_window import BlackjackGUI
    except ImportError as e:
        missing_deps.append(f"GUI modules ({str(e)})")
    
    return missing_deps

def show_startup_info():
    """Display startup information."""
    print("=" * 60)
    print("BlackJack Bot ML - Phase 4 GUI Interface")
    print("=" * 60)
    print("🎯 Comprehensive AI Strategy Advisor")
    print("🎮 Button-based card selection interface")
    print("🤖 Integration with all AI agent types")
    print("📊 Real-time advice with confidence levels")
    print("⚙️  Configurable game rules and settings")
    print("=" * 60)
    print()

def show_help():
    """Display help information."""
    help_text = """
BlackJack Bot ML - GUI Interface Help

GETTING STARTED:
1. Select dealer's upcard using the rank buttons (A, 2-10, J, Q, K)
2. Select player cards by clicking rank buttons multiple times
3. Choose an AI agent from the dropdown menu
4. View the recommended action with confidence level and reasoning

CARD SELECTION:
- Dealer: Click once to select the dealer's face-up card
- Player: Click multiple times to add multiple cards to your hand
- Use "Clear" buttons to reset selections
- Maximum 10 player cards supported for display

AI AGENTS:
- Basic Strategy: Perfect mathematical strategy (100% accuracy)
- Cautious Persona: Conservative player (95% accuracy)
- Aggressive Persona: Action-oriented player (90% accuracy)
- Intuitive Persona: Emotional player (70% accuracy)
- Dynamic Persona: Switches between personas for detection avoidance
- RL Agent: Deep learning agent (requires training)

FEATURES:
- Real-time advice updates as you select cards
- Confidence levels and detailed reasoning for recommendations
- Alternative action suggestions
- Hand analysis with blackjack terminology
- Configurable game rules (H17, DAS, number of decks, etc.)
- Visual card display with suit symbols

KEYBOARD SHORTCUTS:
- Ctrl+R: Reset all card selections
- Ctrl+Q: Quit application
- F1: Show help dialog

CONFIGURATION:
- Game Rules: Adjust number of decks, dealer rules, payouts
- Display: Toggle probabilities, card count, auto-refresh
- AI Agents: Set default agent, confidence display options
- Advanced: Simulation settings, logging, debug mode

For more information, see the project documentation and README.md.
    """
    print(help_text)

def main():
    """Main entry point for the GUI application."""
    try:
        # Show startup information
        show_startup_info()
        
        # Check command line arguments
        if len(sys.argv) > 1:
            if sys.argv[1] in ['-h', '--help', 'help']:
                show_help()
                return 0
            elif sys.argv[1] in ['-v', '--version', 'version']:
                print("BlackJack Bot ML GUI v1.0.0")
                print("Phase 4: GUI Interface Implementation")
                return 0
        
        # Check dependencies
        print("🔍 Checking dependencies...")
        missing_deps = check_dependencies()
        
        if missing_deps:
            print("❌ Missing dependencies:")
            for dep in missing_deps:
                print(f"   - {dep}")
            print("\nPlease install missing dependencies and try again.")
            print("See README.md for installation instructions.")
            return 1
        
        print("✅ All dependencies found")
        print()
        
        # Import GUI after dependency check
        from gui.main_window import BlackjackGUI
        
        # Create and run the GUI application
        print("🚀 Starting BlackJack Bot ML GUI...")
        print("   Close this terminal window to quit the application")
        print("   Use Ctrl+Q in the GUI to quit gracefully")
        print()
        
        # Initialize and run the GUI
        app = BlackjackGUI()
        
        print("✅ GUI initialized successfully")
        print("🎮 Ready for card selection and AI advice!")
        print()
        
        # Start the main event loop
        app.run()
        
        print("👋 BlackJack Bot ML GUI closed")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Error starting GUI application:")
        print(f"   {str(e)}")
        print("\n🔧 Debug information:")
        traceback.print_exc()
        print("\n💡 Troubleshooting:")
        print("   1. Check that all dependencies are installed")
        print("   2. Verify Python version is 3.8 or higher")
        print("   3. Ensure tkinter is available (python -m tkinter)")
        print("   4. Check project file structure is intact")
        print("   5. See README.md for detailed setup instructions")
        return 1

def run_tests():
    """Run basic GUI tests."""
    print("🧪 Running GUI tests...")
    
    try:
        # Test imports
        from gui.main_window import BlackjackGUI
        from gui.card_selector import CardSelectorWidget
        from gui.game_display import GameDisplayWidget
        from gui.advice_engine import AdviceEngineGUI
        from gui.config_panel import ConfigPanelWidget
        
        print("✅ All GUI modules import successfully")
        
        # Test basic functionality
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide test window
        
        # Test widget creation
        frame = tk.Frame(root)
        card_selector = CardSelectorWidget(frame)
        game_display = GameDisplayWidget(frame)
        advice_engine = AdviceEngineGUI(frame)
        config_panel = ConfigPanelWidget(frame)
        
        print("✅ All GUI widgets create successfully")
        
        root.destroy()
        
        print("✅ GUI tests passed")
        return True
        
    except Exception as e:
        print(f"❌ GUI tests failed: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Handle special commands
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            success = run_tests()
            sys.exit(0 if success else 1)
        elif sys.argv[1] in ['help', '-h', '--help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['version', '-v', '--version']:
            print("BlackJack Bot ML GUI v1.0.0")
            sys.exit(0)
    
    # Run the main application
    exit_code = main()
    sys.exit(exit_code)
