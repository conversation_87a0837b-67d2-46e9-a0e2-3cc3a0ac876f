"""
Training Script for Phase 3: Reinforcement Learning with Evasion.

This script demonstrates the complete Phase 3 implementation including:
- RL Agent Foundation (P3_T1)
- Evasion Strategy Integration (P3_T2)
- Adaptive Learning System (P3_T3)
- Training Pipeline (P3_T4)
"""

import sys
import os
import time
import argparse
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rl.training_pipeline import <PERSON><PERSON><PERSON>eline, TrainingConfig, TrainingPhase
from rl.dqn_agent import DQNConfig
from rl.evasion_strategies import EvasionConfig, EvasionTechnique
from rl.adaptive_learning import AdaptationConfig
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from personas.cautious_persona import CautiousPersona
from personas.aggressive_persona import AggressivePersona
from personas.basic_strategy_persona import BasicStrategyPersona


def create_training_configs(test_mode=False):
    """Create training configurations for Phase 3.

    Args:
        test_mode: If True, use minimal configuration for quick testing
    """
    
    # DQN Configuration
    dqn_config = DQNConfig(
        hidden_layers=[128, 64, 32],
        learning_rate=0.001,
        batch_size=32,
        target_update_frequency=100,
        min_buffer_size=1000,
        buffer_size=10000,
        discount_factor=0.99
    )
    
    # Evasion Configuration
    evasion_config = EvasionConfig(
        consistency_threshold=0.92,
        pattern_detection_window=100,
        noise_intensity=0.15,
        technique_weights={
            EvasionTechnique.PERSONA_SWITCHING: 0.25,
            EvasionTechnique.BEHAVIORAL_NOISE: 0.25,
            EvasionTechnique.TIMING_VARIATION: 0.15,
            EvasionTechnique.DECISION_MASKING: 0.15,
            EvasionTechnique.PATTERN_DISRUPTION: 0.1,
            EvasionTechnique.ADAPTIVE_CONSISTENCY: 0.1
        },
        target_consistency_range=(0.75, 0.88)
    )
    
    # Adaptive Learning Configuration
    adaptation_config = AdaptationConfig(
        performance_window=100,
        performance_threshold=-0.05,
        min_adaptation_interval=50,
        lr_adaptation_enabled=True,
        exploration_adaptation_enabled=True,
        persona_adaptation_enabled=True,
        evasion_adaptation_enabled=True,
        consistency_adaptation_enabled=True,
        detection_risk_threshold=0.8
    )
    
    # Training Configuration
    if test_mode:
        # Quick test configuration
        training_config = TrainingConfig(
            total_episodes=5,
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 2,
                TrainingPhase.LEARNING: 2,
                TrainingPhase.OPTIMIZATION: 1,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=3,  # Evaluate after episode 3
            evaluation_episodes=2,   # Only 2 evaluation episodes
            checkpoint_frequency=100,  # No checkpoints in test
            target_win_rate=0.4,
            target_consistency=0.8,
            max_detection_risk=0.3,
            early_stopping_enabled=False,  # Disable early stopping
            patience=100,
            log_frequency=1  # Show progress every episode
        )
    else:
        # Full training configuration
        training_config = TrainingConfig(
            total_episodes=5000,
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 1000,
                TrainingPhase.LEARNING: 2500,
                TrainingPhase.OPTIMIZATION: 1000,
                TrainingPhase.EVALUATION: 500
            },
            evaluation_frequency=250,
            evaluation_episodes=50,
            checkpoint_frequency=500,
            target_win_rate=0.42,
            target_consistency=0.82,
            max_detection_risk=0.25,
            early_stopping_enabled=True,
            patience=1000,
            log_frequency=50
        )
    
    return dqn_config, evasion_config, adaptation_config, training_config


def create_persona_switcher():
    """Create persona switcher with multiple personas."""
    
    # Create personas
    personas = {
        "cautious": CautiousPersona(),
        "aggressive": AggressivePersona(),
        "basic_strategy": BasicStrategyPersona()
    }
    
    # Switch configuration
    switch_config = SwitchConfig(
        min_hands_per_persona=20,
        max_hands_per_persona=100,
        consistency_threshold=0.9
    )
    
    # Create persona switcher
    persona_switcher = PersonaSwitcher(switch_config)
    for name, persona in personas.items():
        persona_switcher.add_persona(name, persona)
    
    return persona_switcher


def setup_training_callbacks(pipeline, verbose=False):
    """Setup training callbacks for monitoring and logging.

    Args:
        pipeline: Training pipeline
        verbose: If True, show detailed progress for each episode
    """

    def episode_callback(pipeline, episode_metrics):
        """Callback executed after each episode."""
        episode = episode_metrics["episode"]
        current_time = time.strftime("%H:%M:%S")

        if verbose:
            # Verbose progress for each episode
            phase = pipeline.metrics.current_phase.value
            total_reward = episode_metrics.get("total_reward", 0)
            steps = episode_metrics.get("steps", 0)
            win_status = "WIN" if episode_metrics.get("win", False) else "LOSS"
            actions = episode_metrics.get("actions", [])
            action_summary = f"{len(actions)} actions" if actions else "No actions"

            print(f"\n[{current_time}] 📊 Episode {episode} Complete:")
            print(f"   Phase: {phase}")
            print(f"   Result: {win_status}")
            print(f"   Reward: {total_reward:.2f}")
            print(f"   Steps: {steps}")
            print(f"   Actions: {action_summary}")

            # Show current persona if available
            if pipeline.persona_switcher and hasattr(pipeline.persona_switcher, 'current_persona_name'):
                print(f"   Current Persona: {pipeline.persona_switcher.current_persona_name}")

        # Log detailed metrics every 100 episodes (or every episode in verbose mode)
        detail_frequency = 1 if verbose else 100
        if episode % detail_frequency == 0:
            agent_stats = pipeline.agent.get_comprehensive_stats()

            if not verbose:
                print(f"\n=== Episode {episode} Detailed Stats ===")

            print(f"   Evasion Metrics:")
            print(f"     Total Evasions: {agent_stats['evasion_metrics']['total_evasions']}")
            print(f"     Avg Detection Risk: {agent_stats['evasion_metrics']['avg_detection_risk']:.3f}")
            print(f"     Evasion Effectiveness: {agent_stats['evasion_metrics']['evasion_effectiveness']:.3f}")

            print(f"   Adaptive Learning:")
            print(f"     Total Adaptations: {agent_stats['adaptive_learning_metrics']['total_adaptations']}")
            print(f"     Last Adaptation: {agent_stats['adaptive_learning_metrics']['last_adaptation_step']}")

            if pipeline.persona_switcher:
                persona_stats = agent_stats['persona_switcher_stats']
                if persona_stats:
                    print(f"   Persona Switching:")
                    print(f"     Current Persona: {persona_stats['current_persona']}")
                    print(f"     Switch History: {persona_stats['switch_history']} switches")
    
    def phase_callback(pipeline, old_phase, new_phase):
        """Callback executed on phase transitions."""
        print(f"\n🔄 PHASE TRANSITION: {old_phase.value} → {new_phase.value}")
        
        # Log phase-specific information
        if new_phase == TrainingPhase.LEARNING:
            print("   Entering learning phase - balancing exploration and exploitation")
        elif new_phase == TrainingPhase.OPTIMIZATION:
            print("   Entering optimization phase - focusing on performance")
        elif new_phase == TrainingPhase.EVALUATION:
            print("   Entering evaluation phase - minimal exploration")
        
        # Get current agent statistics
        agent_stats = pipeline.agent.get_comprehensive_stats()
        print(f"   Current Win Rate: {pipeline.metrics.win_rate_history[-1]:.3f}")
        print(f"   Current Detection Risk: {agent_stats['detection_assessment']['current_risk']:.3f}")
    
    # Add callbacks to pipeline
    pipeline.add_episode_callback(episode_callback)
    pipeline.add_phase_callback(phase_callback)


def main():
    """Main training function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Phase 3 BlackJack Bot ML Training")
    parser.add_argument("--test", action="store_true", help="Run in test mode (5 episodes)")
    parser.add_argument("--verbose", action="store_true", help="Show verbose progress for each episode")
    args = parser.parse_args()

    # Determine mode
    test_mode = args.test
    verbose = args.verbose or test_mode  # Always verbose in test mode

    mode_str = "TEST MODE" if test_mode else "FULL TRAINING"
    print(f"🎯 BlackJack Bot ML - Phase 3 Training ({mode_str})")
    print("=" * 60)

    if test_mode:
        print("🧪 Running in test mode:")
        print("   - 5 total episodes")
        print("   - 2 evaluation episodes")
        print("   - Verbose progress indicators")
        print("   - No early stopping")
        print("-" * 60)

    # Create configurations
    print("📋 Creating training configurations...")
    dqn_config, evasion_config, adaptation_config, training_config = create_training_configs(test_mode)
    
    # Create persona switcher
    print("🎭 Setting up persona switcher...")
    persona_switcher = create_persona_switcher()
    
    # Create training pipeline
    print("🏗️  Initializing training pipeline...")
    pipeline = TrainingPipeline(
        dqn_config=dqn_config,
        evasion_config=evasion_config,
        adaptation_config=adaptation_config,
        training_config=training_config,
        persona_switcher=persona_switcher
    )
    
    # Setup callbacks
    print("📊 Setting up training callbacks...")
    setup_training_callbacks(pipeline, verbose)
    
    # Initialize training
    print("🚀 Initializing training components...")
    pipeline.initialize_training()
    
    # Display training information
    print(f"\n📈 Training Configuration:")
    print(f"   Total Episodes: {training_config.total_episodes}")
    print(f"   Target Win Rate: {training_config.target_win_rate}")
    print(f"   Max Detection Risk: {training_config.max_detection_risk}")
    print(f"   Early Stopping: {training_config.early_stopping_enabled}")
    
    print(f"\n🧠 Agent Configuration:")
    print(f"   Network: {dqn_config.hidden_layers}")
    print(f"   Learning Rate: {dqn_config.learning_rate}")
    print(f"   Batch Size: {dqn_config.batch_size}")
    
    print(f"\n🛡️  Evasion Configuration:")
    print(f"   Consistency Threshold: {evasion_config.consistency_threshold}")
    print(f"   Noise Intensity: {evasion_config.noise_intensity}")
    print(f"   Active Techniques: {len(evasion_config.technique_weights)}")
    
    print(f"\n🔄 Adaptive Learning:")
    print(f"   Performance Window: {adaptation_config.performance_window}")
    print(f"   Adaptation Interval: {adaptation_config.min_adaptation_interval}")
    print(f"   Detection Threshold: {adaptation_config.detection_risk_threshold}")
    
    # Start training
    start_time = time.time()
    start_timestamp = time.strftime("%H:%M:%S")
    print(f"\n🎯 Starting training at {start_timestamp}...")

    if test_mode:
        print("📈 Expected progression: Episodes 0 → 1 → 2 → 3 (evaluation) → 4")

    print("=" * 60)

    try:
        # Execute training
        _ = pipeline.train()  # Ignore return value to avoid unused variable warning
        
        # Calculate training duration
        end_time = time.time()
        training_duration = end_time - start_time
        end_timestamp = time.strftime("%H:%M:%S")

        # Display final results
        print("\n" + "=" * 60)
        print("🏆 TRAINING COMPLETED")
        print("=" * 60)

        print(f"⏱️  Training Duration:")
        print(f"   Started: {start_timestamp}")
        print(f"   Ended: {end_timestamp}")
        print(f"   Total Time: {training_duration:.2f} seconds")

        training_summary = pipeline.get_training_summary()
        performance = training_summary["performance_summary"]

        print(f"\n📊 Final Results:")
        print(f"   Episodes Completed: {pipeline.metrics.current_episode}")
        print(f"   Best Win Rate: {performance['best_win_rate']:.3f} (Episode {performance['best_episode']})")
        print(f"   Final Win Rate: {performance['final_win_rate']:.3f}")
        print(f"   Episodes/Second: {performance['episodes_per_second']:.2f}")
        
        # Get final agent statistics
        if pipeline.agent:
            final_stats = pipeline.agent.get_comprehensive_stats()
            
            print(f"\n🛡️  Final Evasion Performance:")
            evasion_metrics = final_stats["evasion_metrics"]
            print(f"   Total Evasions: {evasion_metrics['total_evasions']}")
            print(f"   Final Detection Risk: {final_stats['detection_assessment']['current_risk']:.3f}")
            print(f"   Evasion Effectiveness: {evasion_metrics['evasion_effectiveness']:.3f}")
            
            print(f"\n🔄 Adaptive Learning Performance:")
            adaptive_metrics = final_stats["adaptive_learning_metrics"]
            print(f"   Total Adaptations: {adaptive_metrics['total_adaptations']}")
            print(f"   Adaptation Effectiveness: {adaptive_metrics['adaptation_effectiveness']:.3f}")
            
            print(f"\n🎭 Persona Switching Performance:")
            persona_stats = final_stats.get("persona_switcher_stats")
            if persona_stats:
                print(f"   Final Persona: {persona_stats['current_persona']}")
                print(f"   Total Switches: {persona_stats['switch_history']}")
            else:
                print("   No persona switching data available")
        
        print(f"\n✅ Training completed successfully!")

        if test_mode:
            print(f"\n🧪 Test Mode Results:")
            print(f"   ✅ All 5 episodes completed")
            print(f"   ✅ Training pipeline functional")
            print(f"   ✅ Episode progression working")
            print(f"   ✅ Evaluation system working")
            print(f"   ✅ All components integrated")
            print(f"   ⏱️  Test completed in {training_duration:.1f} seconds")
        else:
            print(f"   Model saved to: {pipeline.save_dir / 'final_model.pkl'}")
        
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
