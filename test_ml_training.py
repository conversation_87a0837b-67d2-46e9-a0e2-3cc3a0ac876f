#!/usr/bin/env python3
"""
Quick ML Training Test for BlackJack Bot ML Phase 4.

This script runs a minimal training session to verify the ML model
can learn and improve its performance.
"""

import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_rl_agent():
    """Test basic RL agent functionality."""
    print("🧪 Testing basic RL agent...")
    
    try:
        from rl.dqn_agent import DQNAgent, DQNConfig
        from rl.rl_environment import BlackjackRLEnvironment
        from core.game_logic import GameAction
        
        # Create minimal DQN configuration
        config = DQNConfig(
            hidden_layers=[16, 8],
            learning_rate=0.01,
            batch_size=4,
            min_buffer_size=10,
            buffer_size=50
        )
        
        # Create agent and environment
        agent = DQNAgent("Test Agent", config)
        env = BlackjackRLEnvironment(num_decks=1)
        
        print(f"  ✅ Agent created: {agent.name}")
        print(f"  ✅ Device: {agent.device}")
        print(f"  ✅ Network: {agent.get_network_info()['total_parameters']} parameters")
        
        # Test basic interaction
        state = env.reset()
        action = agent.get_action_from_state(state, [GameAction.HIT, GameAction.STAND])
        print(f"  ✅ Agent decision: {action.value}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Basic RL test failed: {e}")
        return False

def test_simple_training():
    """Test simple training loop."""
    print("\n🚀 Testing simple training loop...")
    
    try:
        from rl.dqn_agent import DQNAgent, DQNConfig
        from rl.rl_environment import BlackjackRLEnvironment
        from core.game_logic import GameAction
        import random
        
        # Create minimal setup
        config = DQNConfig(
            hidden_layers=[16, 8],
            learning_rate=0.05,  # Higher learning rate
            batch_size=4,
            min_buffer_size=5,
            buffer_size=20,
            epsilon_start=0.8,
            epsilon_end=0.2,
            epsilon_decay=0.9
        )
        
        agent = DQNAgent("Training Test Agent", config)
        env = BlackjackRLEnvironment(num_decks=1)
        
        print(f"  ✅ Training setup created")
        
        # Record initial performance
        initial_stats = agent.get_stats()
        initial_loss = initial_stats.get('avg_loss', 0.0)
        initial_epsilon = agent.epsilon
        
        print(f"  📊 Initial state:")
        print(f"     Epsilon: {initial_epsilon:.3f}")
        print(f"     Avg Loss: {initial_loss:.3f}")
        print(f"     Training Steps: {agent.training_step}")
        
        # Run simple training episodes
        print(f"  🎯 Running 20 training episodes...")
        
        total_reward = 0
        episodes_completed = 0
        
        for episode in range(20):
            state = env.reset()
            episode_reward = 0
            steps = 0
            
            while not env.is_done() and steps < 10:  # Limit steps per episode
                # Get available actions
                available_actions = env.get_available_actions()
                if not available_actions:
                    break
                
                # Agent selects action
                action = agent.get_action_from_state(state, available_actions)
                
                # Take action in environment
                next_state, reward, done, info = env.step(action)
                
                # Store experience and train
                agent.store_experience(state, action, reward.total, next_state, done)
                
                if agent.can_train():
                    loss = agent.train_step()
                
                state = next_state
                episode_reward += reward.total
                steps += 1
                
                if done:
                    break
            
            total_reward += episode_reward
            episodes_completed += 1
            
            if (episode + 1) % 5 == 0:
                avg_reward = total_reward / episodes_completed
                print(f"     Episode {episode + 1}: Avg Reward = {avg_reward:.3f}")
        
        # Check final performance
        final_stats = agent.get_stats()
        final_loss = final_stats.get('avg_loss', 0.0)
        final_epsilon = agent.epsilon
        final_training_steps = agent.training_step
        
        print(f"  📈 Final state:")
        print(f"     Epsilon: {final_epsilon:.3f} (change: {final_epsilon - initial_epsilon:+.3f})")
        print(f"     Avg Loss: {final_loss:.3f} (change: {final_loss - initial_loss:+.3f})")
        print(f"     Training Steps: {final_training_steps} (new: {final_training_steps})")
        print(f"     Total Episodes: {episodes_completed}")
        print(f"     Avg Episode Reward: {total_reward / episodes_completed:.3f}")
        
        # Check for learning indicators
        learning_detected = False
        
        if final_training_steps > 10:
            learning_detected = True
            print(f"  ✅ Training occurred: {final_training_steps} training steps")
        
        if abs(final_epsilon - initial_epsilon) > 0.1:
            learning_detected = True
            print(f"  ✅ Exploration decay: epsilon changed by {final_epsilon - initial_epsilon:+.3f}")
        
        if final_loss > 0 and final_loss != initial_loss:
            learning_detected = True
            print(f"  ✅ Network learning: loss changed to {final_loss:.3f}")
        
        return learning_detected
        
    except Exception as e:
        print(f"  ❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evasive_agent():
    """Test evasive agent with minimal training."""
    print("\n🛡️ Testing evasive agent...")
    
    try:
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        from rl.adaptive_learning import AdaptationConfig
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        from core.game_logic import GameState, GameAction
        from core.hand import Hand
        from core.card import Card, Suit, Rank
        
        # Create configurations
        dqn_config = DQNConfig(hidden_layers=[16, 8], batch_size=4, min_buffer_size=5)
        evasion_config = EvasionConfig(noise_intensity=0.1)
        adaptation_config = AdaptationConfig(min_adaptation_interval=3)
        
        # Create persona switcher
        switch_config = SwitchConfig(min_hands_per_persona=2, max_hands_per_persona=5)
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona("cautious", CautiousPersona())
        
        # Create evasive agent
        agent = EvasiveDQNAgent(
            "Evasive Test Agent",
            dqn_config,
            evasion_config,
            adaptation_config,
            persona_switcher=persona_switcher
        )
        
        print(f"  ✅ Evasive agent created: {agent.name}")
        
        # Test decision making with evasion
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Test multiple decisions
        decisions = []
        for i in range(5):
            action = agent.get_action(game_state)
            decisions.append(action.value)
            agent.update_experience(1.0, game_state, False)
        
        print(f"  ✅ Decisions made: {decisions}")
        
        # Check evasion system
        stats = agent.get_comprehensive_stats()
        evasion_metrics = stats.get('evasion_metrics', {})
        adaptive_metrics = stats.get('adaptive_learning_metrics', {})
        
        print(f"  📊 Evasion metrics:")
        print(f"     Total evasions: {evasion_metrics.get('total_evasions', 0)}")
        print(f"     Adaptations: {adaptive_metrics.get('total_adaptations', 0)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Evasive agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all ML training tests."""
    print("🎯 BlackJack Bot ML - Training Verification")
    print("=" * 50)
    
    tests = [
        ("Basic RL Agent", test_basic_rl_agent),
        ("Simple Training", test_simple_training),
        ("Evasive Agent", test_evasive_agent)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        start_time = time.time()
        success = test_func()
        duration = time.time() - start_time
        
        if success:
            print(f"  ✅ {test_name} PASSED ({duration:.2f}s)")
            passed += 1
        else:
            print(f"  ❌ {test_name} FAILED ({duration:.2f}s)")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TRAINING TESTS PASSED!")
        print("✅ ML model can learn and improve")
        print("✅ Evasion and adaptation systems working")
        print("✅ Ready for production use through GUI")
    else:
        print("❌ Some training tests failed")
        print("Please review the errors above")
    
    print(f"\n🎮 GUI Integration Ready!")
    print("   Launch the GUI with: python run_gui.py")
    print("   The trained ML agents are available in the GUI")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
