"""
Main GUI Window for BlackJack Bot ML - Phase 4.

This module implements the primary application window with layout management,
styling, and integration of all GUI components.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from .card_selector import CardSelectorWidget
from .game_display import GameDisplayWidget
from .advice_engine import AdviceEngineG<PERSON>
from .config_panel import ConfigPanelWidget


class BlackjackGUI:
    """
    Main GUI application for BlackJack Bot ML.
    
    Provides a comprehensive interface for card selection, ML advice,
    and game state visualization.
    """
    
    def __init__(self):
        """Initialize the main GUI application."""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_style()
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()
        
    def setup_window(self):
        """Configure the main window properties."""
        self.root.title("BlackJack Bot ML - Phase 4 GUI Interface")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
        # Configure window icon and properties
        self.root.resizable(True, True)
        
    def setup_style(self):
        """Configure the application styling and theme."""
        self.style = ttk.Style()
        
        # Configure modern theme
        self.style.theme_use('clam')
        
        # Define color scheme
        self.colors = {
            'primary': '#2E3440',      # Dark blue-gray
            'secondary': '#3B4252',    # Lighter blue-gray
            'accent': '#5E81AC',       # Blue accent
            'success': '#A3BE8C',      # Green
            'warning': '#EBCB8B',      # Yellow
            'danger': '#BF616A',       # Red
            'background': '#ECEFF4',   # Light gray
            'text': '#2E3440',         # Dark text
            'text_light': '#4C566A'    # Light text
        }
        
        # Configure styles
        self.style.configure('Title.TLabel', 
                           font=('Arial', 16, 'bold'),
                           foreground=self.colors['primary'])
        
        self.style.configure('Heading.TLabel',
                           font=('Arial', 12, 'bold'),
                           foreground=self.colors['primary'])
        
        self.style.configure('Card.TButton',
                           font=('Arial', 10, 'bold'),
                           padding=(10, 5))
        
        self.style.configure('Action.TButton',
                           font=('Arial', 11, 'bold'),
                           padding=(15, 8))
        
        # Configure root background
        self.root.configure(bg=self.colors['background'])
        
    def create_widgets(self):
        """Create all GUI widgets and components."""
        # Main container frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Title label
        self.title_label = ttk.Label(
            self.main_frame,
            text="BlackJack Bot ML - AI Strategy Advisor",
            style='Title.TLabel'
        )
        
        # Create main content areas
        self.create_left_panel()
        self.create_center_panel()
        self.create_right_panel()
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready - Select cards to get AI advice",
            foreground=self.colors['text_light']
        )
        
    def create_left_panel(self):
        """Create the left panel with card selection."""
        self.left_frame = ttk.LabelFrame(
            self.main_frame,
            text="Card Selection",
            padding="10"
        )
        
        # Card selector widget
        self.card_selector = CardSelectorWidget(
            self.left_frame,
            on_selection_change=self.on_card_selection_change
        )
        
    def create_center_panel(self):
        """Create the center panel with game display."""
        self.center_frame = ttk.LabelFrame(
            self.main_frame,
            text="Game State",
            padding="10"
        )
        
        # Game display widget
        self.game_display = GameDisplayWidget(self.center_frame)
        
    def create_right_panel(self):
        """Create the right panel with advice and configuration."""
        self.right_frame = ttk.Frame(self.main_frame)
        
        # Advice engine widget
        self.advice_frame = ttk.LabelFrame(
            self.right_frame,
            text="AI Advice",
            padding="10"
        )
        self.advice_engine = AdviceEngineGUI(
            self.advice_frame,
            on_advice_update=self.on_advice_update
        )
        
        # Configuration panel
        self.config_frame = ttk.LabelFrame(
            self.right_frame,
            text="Settings",
            padding="10"
        )
        self.config_panel = ConfigPanelWidget(
            self.config_frame,
            on_config_change=self.on_config_change
        )
        
    def setup_layout(self):
        """Configure the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure root grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Configure main frame grid weights
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=2)
        self.main_frame.grid_columnconfigure(2, weight=1)
        
        # Title
        self.title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Main panels
        self.left_frame.grid(row=1, column=0, sticky='nsew', padx=(0, 10))
        self.center_frame.grid(row=1, column=1, sticky='nsew', padx=5)
        self.right_frame.grid(row=1, column=2, sticky='nsew', padx=(10, 0))
        
        # Right panel layout
        self.right_frame.grid_rowconfigure(0, weight=2)
        self.right_frame.grid_rowconfigure(1, weight=1)
        self.right_frame.grid_columnconfigure(0, weight=1)
        
        self.advice_frame.grid(row=0, column=0, sticky='nsew', pady=(0, 10))
        self.config_frame.grid(row=1, column=0, sticky='nsew')
        
        # Status bar
        self.status_frame.grid(row=2, column=0, columnspan=3, sticky='ew', pady=(20, 0))
        self.status_label.grid(row=0, column=0, sticky='w')
        
        # Layout child widgets
        self.card_selector.setup_layout()
        self.game_display.setup_layout()
        self.advice_engine.setup_layout()
        self.config_panel.setup_layout()
        
    def setup_bindings(self):
        """Set up event bindings and keyboard shortcuts."""
        # Keyboard shortcuts
        self.root.bind('<Control-r>', lambda e: self.reset_game())
        self.root.bind('<Control-q>', lambda e: self.quit_application())
        self.root.bind('<F1>', lambda e: self.show_help())
        
        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self.quit_application)
        
    def on_card_selection_change(self, dealer_cards, player_cards):
        """Handle card selection changes."""
        try:
            # Update game display
            self.game_display.update_cards(dealer_cards, player_cards)
            
            # Get AI advice if we have valid cards
            if dealer_cards and player_cards:
                self.advice_engine.get_advice(dealer_cards, player_cards)
                self.update_status("AI advice updated")
            else:
                self.advice_engine.clear_advice()
                self.update_status("Select cards to get AI advice")
                
        except Exception as e:
            self.show_error(f"Error processing card selection: {str(e)}")
            
    def on_advice_update(self, advice_data):
        """Handle advice engine updates."""
        self.update_status(f"Advice: {advice_data.get('action', 'Unknown')}")
        
    def on_config_change(self, config_data):
        """Handle configuration changes."""
        try:
            # Update advice engine with new configuration
            self.advice_engine.update_config(config_data)
            self.update_status("Configuration updated")
        except Exception as e:
            self.show_error(f"Error updating configuration: {str(e)}")
            
    def reset_game(self):
        """Reset the game to initial state."""
        self.card_selector.reset()
        self.game_display.reset()
        self.advice_engine.clear_advice()
        self.update_status("Game reset - Select cards to get AI advice")
        
    def update_status(self, message):
        """Update the status bar message."""
        self.status_label.config(text=message)
        
    def show_error(self, message):
        """Show an error message to the user."""
        messagebox.showerror("Error", message)
        self.update_status(f"Error: {message}")
        
    def show_help(self):
        """Show help information."""
        help_text = """
BlackJack Bot ML - GUI Interface Help

Card Selection:
- Click rank buttons (A, 2-10, J, Q, K) to select cards
- Choose dealer upcard first, then player cards
- Use Clear buttons to reset selections

AI Advice:
- Select an AI agent from the dropdown
- View recommended actions with confidence levels
- Compare different agent strategies

Keyboard Shortcuts:
- Ctrl+R: Reset game
- Ctrl+Q: Quit application
- F1: Show this help

For more information, see the project documentation.
        """
        messagebox.showinfo("Help", help_text)
        
    def quit_application(self):
        """Safely quit the application."""
        if messagebox.askokcancel("Quit", "Are you sure you want to quit?"):
            self.root.quit()
            self.root.destroy()
            
    def run(self):
        """Start the GUI application."""
        try:
            self.update_status("BlackJack Bot ML GUI started successfully")
            self.root.mainloop()
        except Exception as e:
            self.show_error(f"Application error: {str(e)}")
            
    def get_root(self):
        """Get the root window for testing purposes."""
        return self.root


if __name__ == "__main__":
    app = BlackjackGUI()
    app.run()
