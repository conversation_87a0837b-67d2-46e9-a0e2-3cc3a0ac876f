"""
Evasion Manager for BlackJack Bot ML.

This module coordinates multiple evasion strategies to avoid detection
while maintaining learning performance.
"""

import random
import time
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .evasion_strategies import (
    BaseEvasionStrategy, EvasionConfig, EvasionTechnique,
    PersonaSwitchingStrategy, BehavioralNoiseStrategy, TimingVariationStrategy,
    DecisionMaskingStrategy, PatternDisruptionStrategy, AdaptiveConsistencyStrategy
)
from .base_rl_agent import BaseRLAgent
from personas.persona_switcher import PersonaSwitcher
from core.game_logic import GameAction, GameState


@dataclass
class EvasionMetrics:
    """Metrics for evasion performance tracking."""
    total_evasions: int = 0
    technique_usage: Dict[EvasionTechnique, int] = None
    detection_risk_history: List[float] = None
    evasion_effectiveness: float = 0.0
    avg_detection_risk: float = 0.0
    
    def __post_init__(self):
        """Initialize default values."""
        if self.technique_usage is None:
            self.technique_usage = {technique: 0 for technique in EvasionTechnique}
        if self.detection_risk_history is None:
            self.detection_risk_history = []


class EvasionManager:
    """
    Manager for coordinating multiple evasion strategies.
    
    Integrates various evasion techniques to avoid detection while
    maintaining learning performance and human-like behavior.
    """
    
    def __init__(self, config: EvasionConfig, persona_switcher: Optional[PersonaSwitcher] = None):
        """
        Initialize evasion manager.
        
        Args:
            config: Evasion configuration
            persona_switcher: Optional persona switcher for persona-based evasion
        """
        self.config = config
        self.persona_switcher = persona_switcher
        
        # Initialize evasion strategies
        self.strategies: Dict[EvasionTechnique, BaseEvasionStrategy] = {}
        self._initialize_strategies()
        
        # Tracking and metrics
        self.metrics = EvasionMetrics()
        self.evasion_history: List[Dict[str, Any]] = []
        self.last_risk_assessment = 0
        
        # Adaptive parameters
        self.current_risk_level = 0.0
        self.evasion_intensity = 1.0
        
    def _initialize_strategies(self) -> None:
        """Initialize all evasion strategies."""
        # Persona switching (if available)
        if self.persona_switcher:
            self.strategies[EvasionTechnique.PERSONA_SWITCHING] = PersonaSwitchingStrategy(
                self.config, self.persona_switcher
            )
        
        # Behavioral noise
        self.strategies[EvasionTechnique.BEHAVIORAL_NOISE] = BehavioralNoiseStrategy(
            self.config
        )
        
        # Timing variation
        self.strategies[EvasionTechnique.TIMING_VARIATION] = TimingVariationStrategy(
            self.config
        )
        
        # Decision masking
        self.strategies[EvasionTechnique.DECISION_MASKING] = DecisionMaskingStrategy(
            self.config
        )
        
        # Pattern disruption
        self.strategies[EvasionTechnique.PATTERN_DISRUPTION] = PatternDisruptionStrategy(
            self.config
        )
        
        # Adaptive consistency
        self.strategies[EvasionTechnique.ADAPTIVE_CONSISTENCY] = AdaptiveConsistencyStrategy(
            self.config
        )
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """
        Apply evasion techniques to the RL agent's action.
        
        Args:
            agent: RL agent making the decision
            game_state: Current game state
            rl_action: Action selected by RL policy
            
        Returns:
            Tuple of (final_action, evasion_metadata)
        """
        # Assess detection risk
        detection_risk = self._assess_detection_risk(agent)
        
        # Update risk tracking
        self.current_risk_level = detection_risk
        self.metrics.detection_risk_history.append(detection_risk)
        if len(self.metrics.detection_risk_history) > 1000:
            self.metrics.detection_risk_history.pop(0)
        
        # Select and apply evasion strategies
        final_action = rl_action
        applied_techniques = []
        evasion_metadata = {
            "detection_risk": detection_risk,
            "original_action": rl_action.value,
            "applied_techniques": [],
            "technique_details": {}
        }
        
        # Apply strategies based on priority and activation conditions
        for technique, strategy in self.strategies.items():
            if strategy.should_activate(agent, detection_risk):
                # Apply strategy
                final_action, technique_metadata = strategy.apply_evasion(
                    agent, game_state, final_action
                )
                
                # Record application
                applied_techniques.append(technique)
                evasion_metadata["applied_techniques"].append(technique.value)
                evasion_metadata["technique_details"][technique.value] = technique_metadata
                
                # Update metrics
                self.metrics.technique_usage[technique] += 1
                self.metrics.total_evasions += 1
                
                # Break if action was significantly modified (avoid over-evasion)
                if final_action != rl_action and technique != EvasionTechnique.TIMING_VARIATION:
                    break
        
        # Record evasion event
        evasion_record = {
            "timestamp": time.time(),
            "training_step": agent.training_step,
            "detection_risk": detection_risk,
            "original_action": rl_action.value,
            "final_action": final_action.value,
            "applied_techniques": [t.value for t in applied_techniques],
            "was_modified": final_action != rl_action
        }
        
        self.evasion_history.append(evasion_record)
        if len(self.evasion_history) > 1000:
            self.evasion_history.pop(0)
        
        # Update final metadata
        evasion_metadata["final_action"] = final_action.value
        evasion_metadata["was_modified"] = final_action != rl_action
        
        return final_action, evasion_metadata
    
    def _assess_detection_risk(self, agent: BaseRLAgent) -> float:
        """
        Assess current detection risk based on agent behavior.
        
        Args:
            agent: RL agent to assess
            
        Returns:
            Detection risk score (0.0 to 1.0)
        """
        risk_factors = []
        
        # Consistency risk
        if len(agent.consistency_scores) > 0:
            consistency = agent.consistency_scores[-1]
            consistency_risk = max(0.0, consistency - self.config.consistency_threshold)
            risk_factors.append(consistency_risk * 2.0)  # Weight consistency highly
        
        # Pattern detection risk
        pattern_risk = self._assess_pattern_risk(agent)
        risk_factors.append(pattern_risk)
        
        # Timing predictability risk
        timing_risk = self._assess_timing_risk(agent)
        risk_factors.append(timing_risk)
        
        # Decision diversity risk
        diversity_risk = self._assess_diversity_risk(agent)
        risk_factors.append(diversity_risk)
        
        # Calculate overall risk
        if risk_factors:
            overall_risk = sum(risk_factors) / len(risk_factors)
        else:
            overall_risk = 0.0
        
        return min(1.0, overall_risk)
    
    def _assess_pattern_risk(self, agent: BaseRLAgent) -> float:
        """Assess risk from detectable patterns."""
        if len(agent.decision_history) < self.config.pattern_detection_window:
            return 0.0
        
        recent_decisions = agent.decision_history[-self.config.pattern_detection_window:]
        
        # Check for repetitive patterns
        action_sequence = [d.get('final_action', d.get('rl_action', 'H')) for d in recent_decisions]
        
        # Simple pattern detection: check for repeated subsequences
        pattern_score = 0.0
        for length in [2, 3, 4]:
            if len(action_sequence) >= length * 2:
                for i in range(len(action_sequence) - length * 2 + 1):
                    pattern = action_sequence[i:i+length]
                    next_pattern = action_sequence[i+length:i+length*2]
                    if pattern == next_pattern:
                        pattern_score += 0.1 * length
        
        return min(1.0, pattern_score)
    
    def _assess_timing_risk(self, agent: BaseRLAgent) -> float:
        """Assess risk from timing predictability."""
        # Check if timing variation strategy is active
        timing_strategy = self.strategies.get(EvasionTechnique.TIMING_VARIATION)
        if not timing_strategy or not hasattr(timing_strategy, 'timing_history'):
            return 0.5  # Medium risk if no timing variation
        
        timing_history = timing_strategy.timing_history
        if len(timing_history) < 10:
            return 0.3
        
        # Calculate timing variance
        timing_variance = np.var(timing_history)
        
        # Low variance = high risk
        if timing_variance < 0.1:
            return 0.8
        elif timing_variance < 0.3:
            return 0.4
        else:
            return 0.1
    
    def _assess_diversity_risk(self, agent: BaseRLAgent) -> float:
        """Assess risk from lack of decision diversity."""
        if len(agent.decision_history) < 20:
            return 0.0
        
        recent_decisions = agent.decision_history[-20:]
        actions = [d.get('final_action', d.get('rl_action', 'H')) for d in recent_decisions]
        
        # Calculate action diversity
        unique_actions = len(set(actions))
        diversity_ratio = unique_actions / len(actions)
        
        # Low diversity = high risk
        if diversity_ratio < 0.3:
            return 0.8
        elif diversity_ratio < 0.5:
            return 0.4
        else:
            return 0.1
    
    def get_evasion_metrics(self) -> EvasionMetrics:
        """Get current evasion metrics."""
        # Update effectiveness
        if len(self.metrics.detection_risk_history) > 5:
            recent_risks = self.metrics.detection_risk_history[-10:]
            self.metrics.avg_detection_risk = sum(recent_risks) / len(recent_risks)

            # Calculate effectiveness based on multiple factors
            effectiveness_factors = []

            # Factor 1: Risk trend analysis (if we have enough history)
            if len(self.metrics.detection_risk_history) > 10:
                older_risks = self.metrics.detection_risk_history[-20:-10] if len(self.metrics.detection_risk_history) > 20 else self.metrics.detection_risk_history[:-10]
                if older_risks:
                    old_avg = sum(older_risks) / len(older_risks)
                    risk_reduction = max(0.0, old_avg - self.metrics.avg_detection_risk)
                    effectiveness_factors.append(min(1.0, risk_reduction * 3.0))

            # Factor 2: Evasion application rate (higher application = higher effectiveness)
            if self.metrics.total_evasions > 0:
                # Assume effectiveness increases with evasion usage
                application_rate = min(1.0, self.metrics.total_evasions / 1000.0)  # Normalize to 1000 evasions
                effectiveness_factors.append(application_rate * 0.7)  # Base effectiveness from usage

            # Factor 3: Risk level management (lower average risk = higher effectiveness)
            if self.metrics.avg_detection_risk < 0.5:
                risk_management_factor = (0.5 - self.metrics.avg_detection_risk) * 2.0
                effectiveness_factors.append(min(1.0, risk_management_factor))

            # Factor 4: Technique diversity (using multiple techniques = higher effectiveness)
            active_techniques = sum(1 for count in self.metrics.technique_usage.values() if count > 0)
            diversity_factor = min(1.0, active_techniques / len(EvasionTechnique)) * 0.8
            effectiveness_factors.append(diversity_factor)

            # Calculate overall effectiveness
            if effectiveness_factors:
                self.metrics.evasion_effectiveness = sum(effectiveness_factors) / len(effectiveness_factors)
            else:
                # Fallback: base effectiveness on evasion activity
                self.metrics.evasion_effectiveness = min(0.5, self.metrics.total_evasions / 2000.0)

        return self.metrics
    
    def get_strategy_stats(self) -> Dict[str, Any]:
        """Get statistics for all evasion strategies."""
        stats = {}
        
        for technique, strategy in self.strategies.items():
            stats[technique.value] = strategy.get_activation_stats()
        
        return stats
    
    def get_evasion_history(self) -> List[Dict[str, Any]]:
        """Get evasion history."""
        return self.evasion_history.copy()
    
    def reset_metrics(self) -> None:
        """Reset evasion metrics."""
        self.metrics = EvasionMetrics()
        self.evasion_history = []
        self.current_risk_level = 0.0
        
        # Reset strategy counters
        for strategy in self.strategies.values():
            strategy.activation_count = 0
            strategy.last_activation = 0.0
    
    def update_config(self, new_config: EvasionConfig) -> None:
        """Update evasion configuration."""
        self.config = new_config
        
        # Update strategy configurations
        for strategy in self.strategies.values():
            strategy.config = new_config
    
    def set_persona_switcher(self, persona_switcher: PersonaSwitcher) -> None:
        """Set or update persona switcher."""
        self.persona_switcher = persona_switcher
        
        # Add or update persona switching strategy
        if persona_switcher:
            self.strategies[EvasionTechnique.PERSONA_SWITCHING] = PersonaSwitchingStrategy(
                self.config, persona_switcher
            )
        elif EvasionTechnique.PERSONA_SWITCHING in self.strategies:
            del self.strategies[EvasionTechnique.PERSONA_SWITCHING]
    
    def get_detection_risk_assessment(self) -> Dict[str, Any]:
        """Get comprehensive detection risk assessment."""
        return {
            "current_risk": self.current_risk_level,
            "risk_level": self._categorize_risk(self.current_risk_level),
            "avg_risk": self.metrics.avg_detection_risk,
            "total_evasions": self.metrics.total_evasions,
            "technique_usage": dict(self.metrics.technique_usage),
            "evasion_effectiveness": self.metrics.evasion_effectiveness,
            "recommendations": self._get_risk_recommendations()
        }
    
    def _categorize_risk(self, risk: float) -> str:
        """Categorize risk level."""
        if risk >= 0.8:
            return "HIGH"
        elif risk >= 0.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _get_risk_recommendations(self) -> List[str]:
        """Get recommendations for risk mitigation."""
        recommendations = []
        
        if self.current_risk_level >= 0.8:
            recommendations.append("Immediate evasion required - activate all strategies")
            recommendations.append("Consider persona switching if available")
            recommendations.append("Increase behavioral noise intensity")
        elif self.current_risk_level >= 0.5:
            recommendations.append("Moderate risk detected - increase evasion frequency")
            recommendations.append("Monitor consistency patterns closely")
        else:
            recommendations.append("Risk level acceptable - maintain current evasion")
        
        # Strategy-specific recommendations
        if self.metrics.technique_usage[EvasionTechnique.BEHAVIORAL_NOISE] == 0:
            recommendations.append("Consider enabling behavioral noise")
        
        if not self.persona_switcher:
            recommendations.append("Persona switching not available - consider enabling")
        
        return recommendations
